{"nodes": {"brew-src": {"flake": false, "locked": {"lastModified": 1751910772, "narHash": "sha256-jQNdIkq2iRDNWskd5f8kX6q9BO/CBSXhMH41WNRft8E=", "owner": "Homebrew", "repo": "brew", "rev": "700d67a85e0129ab8a893ff69246943479e33df1", "type": "github"}, "original": {"owner": "Homebrew", "ref": "4.5.9", "repo": "brew", "type": "github"}}, "darwin": {"inputs": {"nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1751313918, "narHash": "sha256-HsJM3XLa43WpG+665aGEh8iS8AfEwOIQWk3Mke3e7nk=", "owner": "LnL7", "repo": "nix-darwin", "rev": "e04a388232d9a6ba56967ce5b53a8a6f713cdfcf", "type": "github"}, "original": {"owner": "LnL7", "repo": "nix-darwin", "type": "github"}}, "devbox": {"inputs": {"flake-utils": "flake-utils", "nixpkgs": "nixpkgs"}, "locked": {"lastModified": 1752512931, "narHash": "sha256-WifEyZZ3tVcbRk4sE5jn5pZLPTVHaPVoTHOyy2SpX1Y=", "owner": "jetify-com", "repo": "devbox", "rev": "a235f7d3bf4c7dc787f2e037bcd45120718d519f", "type": "github"}, "original": {"owner": "jetify-com", "repo": "devbox", "type": "github"}}, "flake-compat": {"flake": false, "locked": {"lastModified": 1650374568, "narHash": "sha256-Z+s0J8/r907g149rllvwhb4pKi8Wam5ij0st8PwAh+E=", "owner": "edols<PERSON>", "repo": "flake-compat", "rev": "b4a34015c698c7793d592d66adbab377907a2be8", "type": "github"}, "original": {"owner": "edols<PERSON>", "repo": "flake-compat", "type": "github"}}, "flake-utils": {"inputs": {"systems": "systems"}, "locked": {"lastModified": 1731533236, "narHash": "sha256-l0KFg5HjrsfsO/JpG+r7fRrqm12kzFHyUHqHCVpMMbI=", "owner": "numtide", "repo": "flake-utils", "rev": "11707dc2f618dd54ca8739b309ec4fc024de578b", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "flake-utils-plus": {"inputs": {"flake-utils": "flake-utils_2"}, "locked": {"lastModified": 1715533576, "narHash": "sha256-fT4ppWeCJ0uR300EH3i7kmgRZnAVxrH+XtK09jQWihk=", "owner": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "repo": "flake-utils-plus", "rev": "3542fe9126dc492e53ddd252bb0260fe035f2c0f", "type": "github"}, "original": {"owner": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "repo": "flake-utils-plus", "rev": "3542fe9126dc492e53ddd252bb0260fe035f2c0f", "type": "github"}}, "flake-utils_2": {"inputs": {"systems": "systems_2"}, "locked": {"lastModified": 1694529238, "narHash": "sha256-zsNZZGTGnMOf9YpHKJqMSsa0dXbfmxeoJ7xHlrt+xmY=", "owner": "numtide", "repo": "flake-utils", "rev": "ff7b65b44d01cf9ba6a71320833626af21126384", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "home-manager": {"inputs": {"nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1752814804, "narHash": "sha256-irfg7lnfEpJY+3Cffkluzp2MTVw1Uq9QGxFp6qadcXI=", "owner": "nix-community", "repo": "home-manager", "rev": "d0300c8808e41da81d6edfc202f3d3833c157daf", "type": "github"}, "original": {"owner": "nix-community", "repo": "home-manager", "type": "github"}}, "nix-homebrew": {"inputs": {"brew-src": "brew-src"}, "locked": {"lastModified": 1752160973, "narHash": "sha256-BCC8KB7TEtwv7vZN1WDu870tRbXtzUcmF9xNr6ws5Wc=", "owner": "zhaofengli-wip", "repo": "nix-homebrew", "rev": "69c1aa2f136f3c3326d9b6770e0eb54f12832971", "type": "github"}, "original": {"owner": "zhaofengli-wip", "repo": "nix-homebrew", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1744463964, "narHash": "sha256-LWqduOgLHCFxiTNYi3Uj5Lgz0SR+Xhw3kr/3Xd0GPTM=", "owner": "NixOS", "repo": "nixpkgs", "rev": "2631b0b7abcea6e640ce31cd78ea58910d31e650", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixos-unstable", "repo": "nixpkgs", "type": "github"}}, "nixpkgs_2": {"locked": {"lastModified": 1752747119, "narHash": "sha256-2Kp9St3Pbsmu+xMsobLcgzzUxPvZR7alVJWyuk2BAPc=", "owner": "nixos", "repo": "nixpkgs", "rev": "fa0ef8a6bb1651aa26c939aeb51b5f499e86b0ec", "type": "github"}, "original": {"owner": "nixos", "ref": "nixpkgs-unstable", "repo": "nixpkgs", "type": "github"}}, "root": {"inputs": {"darwin": "darwin", "devbox": "devbox", "home-manager": "home-manager", "nix-homebrew": "nix-homebrew", "nixpkgs": "nixpkgs_2", "snowfall-lib": "snowfall-lib"}}, "snowfall-lib": {"inputs": {"flake-compat": "flake-compat", "flake-utils-plus": "flake-utils-plus", "nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1736130495, "narHash": "sha256-4i9nAJEZFv7vZMmrE0YG55I3Ggrtfo5/T07JEpEZ/RM=", "owner": "snowfallorg", "repo": "lib", "rev": "02d941739f98a09e81f3d2d9b3ab08918958beac", "type": "github"}, "original": {"owner": "snowfallorg", "repo": "lib", "type": "github"}}, "systems": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}, "systems_2": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}}, "root": "root", "version": 7}