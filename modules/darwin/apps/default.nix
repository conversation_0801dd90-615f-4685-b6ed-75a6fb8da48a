# Darwin applications and packages module
{ lib, pkgs, config, namespace, ... }:

with lib;
with lib.${namespace};

let
  cfg = config.${namespace}.apps;
in
{
  options.${namespace}.apps = {
    enable = mkBoolOpt false "Whether to enable applications configuration.";

    # Application categories
    development = mkAppCategoryOptions "development" [
      "visual-studio-code"
      "tableplus"
      "lens"
    ];

    productivity = mkAppCategoryOptions "productivity" [
      "arc"
      "raycast"
      "popclip"
      "bitwarden"
      "keynote"
      "numbers"
      "pages"
      "the-unarchiver"
    ];

    media = mkAppCategoryOptions "media" [
      "iina"
      "spotify"
      "sonos"
    ];

    system = mkAppCategoryOptions "system" [
      "adguard"
      "airbuddy"
      "betterdisplay"
      "itsycal"
      "jordanbaird-ice"
      "karabiner-elements"
      "surge"
    ];

    communication = mkAppCategoryOptions "communication" [
      "dingtalk"
      "line"
    ];

    creative = mkAppCategoryOptions "creative" [
      "bambu-studio"
      "thumbhost3mf"
    ];

    gaming = mkAppCategoryOptions "gaming" [
      "sony-ps-remote-play"
    ];
  };

  config = mkIf cfg.enable (
    let
      # Application definitions mapping
      appDefinitions = {
        # Development applications
        "visual-studio-code" = {
          cask = "visual-studio-code";
          description = "Microsoft Visual Studio Code";
        };
        "tableplus" = {
          cask = "tableplus";
          description = "Database management tool";
        };
        "lens" = {
          cask = "lens";
          description = "Kubernetes IDE";
        };

        # Productivity applications
        "arc" = {
          cask = "arc";
          description = "Arc web browser";
        };
        "raycast" = {
          cask = "raycast";
          description = "Productivity launcher";
        };
        "popclip" = {
          masApp = { name = "PopClip"; id = 445189367; };
          description = "Text selection popup tool";
        };
        "bitwarden" = {
          masApp = { name = "Bitwarden"; id = 1352778147; };
          brew = "bitwarden-cli";
          description = "Password manager";
        };
        "keynote" = {
          masApp = { name = "Keynote"; id = 409183694; };
          description = "Apple presentation software";
        };
        "numbers" = {
          masApp = { name = "Numbers"; id = 409203825; };
          description = "Apple spreadsheet software";
        };
        "pages" = {
          masApp = { name = "Pages"; id = 409201541; };
          description = "Apple word processor";
        };
        "the-unarchiver" = {
          masApp = { name = "The Unarchiver"; id = 425424353; };
          description = "Archive extraction utility";
        };

        # Media applications
        "iina" = {
          package = pkgs.iina;
          description = "Modern media player";
        };
        "spotify" = {
          cask = "spotify";
          description = "Music streaming service";
        };
        "sonos" = {
          cask = "sonos";
          description = "Sonos system controller";
        };

        # System applications
        "adguard" = {
          cask = "adguard";
          description = "Ad blocker and privacy protection";
        };
        "airbuddy" = {
          cask = "airbuddy";
          description = "AirPods connection helper";
        };
        "betterdisplay" = {
          cask = "betterdisplay";
          description = "Display management tool";
        };
        "itsycal" = {
          cask = "itsycal";
          description = "Menu bar calendar";
        };
        "jordanbaird-ice" = {
          cask = "jordanbaird-ice";
          description = "Menu bar management tool";
        };
        "karabiner-elements" = {
          cask = "karabiner-elements";
          description = "Keyboard customization tool";
        };
        "surge" = {
          cask = "surge";
          description = "Network debugging tool";
        };

        # Communication applications
        "dingtalk" = {
          cask = "dingtalk";
          description = "Enterprise communication platform";
        };
        "line" = {
          masApp = { name = "LINE"; id = 539883307; };
          description = "Messaging application";
        };

        # Creative applications
        "bambu-studio" = {
          cask = "bambu-studio";
          description = "3D printing slicer";
        };
        "thumbhost3mf" = {
          cask = "thumbhost3mf";
          description = "3MF file viewer";
        };

        # Gaming applications
        "sony-ps-remote-play" = {
          cask = "sony-ps-remote-play";
          description = "PlayStation Remote Play";
        };
      };

      # Helper function to get enabled apps for a category
      getEnabledApps = category:
        let
          categoryConfig = cfg.${category};
        in
        if categoryConfig.enable
        then subtractLists categoryConfig.excludeApps categoryConfig.apps
        else [];

      # Get all enabled apps across all categories
      allEnabledApps = flatten [
        (getEnabledApps "development")
        (getEnabledApps "productivity")
        (getEnabledApps "media")
        (getEnabledApps "system")
        (getEnabledApps "communication")
        (getEnabledApps "creative")
        (getEnabledApps "gaming")
      ];

      # Extract packages, casks, brews, and masApps from enabled apps
      enabledPackages = flatten (map (app:
        let appDef = appDefinitions.${app} or {}; in
        optional (appDef ? package) appDef.package
      ) allEnabledApps);

      enabledCasks = flatten (map (app:
        let appDef = appDefinitions.${app} or {}; in
        optional (appDef ? cask) appDef.cask
      ) allEnabledApps);

      enabledBrews = flatten (map (app:
        let appDef = appDefinitions.${app} or {}; in
        optional (appDef ? brew) appDef.brew
      ) allEnabledApps);

      enabledMasApps = listToAttrs (flatten (map (app:
        let appDef = appDefinitions.${app} or {}; in
        optional (appDef ? masApp) (nameValuePair appDef.masApp.name appDef.masApp.id)
      ) allEnabledApps));

      # Collect extra packages/casks/brews from all enabled categories
      extraPackages = flatten (map (category:
        let categoryConfig = cfg.${category}; in
        if categoryConfig.enable then categoryConfig.extraPackages else []
      ) ["development" "productivity" "media" "system" "communication" "creative" "gaming"]);

      extraCasks = flatten (map (category:
        let categoryConfig = cfg.${category}; in
        if categoryConfig.enable then categoryConfig.extraCasks else []
      ) ["development" "productivity" "media" "system" "communication" "creative" "gaming"]);

      extraBrews = flatten (map (category:
        let categoryConfig = cfg.${category}; in
        if categoryConfig.enable then categoryConfig.extraBrews else []
      ) ["development" "productivity" "media" "system" "communication" "creative" "gaming"]);

      extraMasApps = foldl' (acc: category:
        let categoryConfig = cfg.${category}; in
        if categoryConfig.enable then acc // categoryConfig.extraMasApps else acc
      ) {} ["development" "productivity" "media" "system" "communication" "creative" "gaming"];

    in {
      # Assertions for configuration validation
      assertions = [
        {
          assertion = all (app: appDefinitions ? ${app}) allEnabledApps;
          message = "Unknown application(s) found: ${toString (filter (app: !(appDefinitions ? ${app})) allEnabledApps)}";
        }
        {
          assertion = length allEnabledApps > 0 -> any (category: cfg.${category}.enable) ["development" "productivity" "media" "system" "communication" "creative" "gaming"];
          message = "At least one application category must be enabled when apps module is enabled";
        }
      ];

      environment = {
        systemPackages = with pkgs; [
          # Core system packages
          devbox
          ffmpeg
          git
          wget
        ] ++ enabledPackages ++ extraPackages;

        variables = {
          EDITOR = "vim";
          HOMEBREW_NO_ANALYTICS = "1";
        };
      };

      homebrew = mkIf (length enabledCasks > 0 || length enabledBrews > 0 || length (attrNames enabledMasApps) > 0 || length extraCasks > 0 || length extraBrews > 0 || length (attrNames extraMasApps) > 0) {
        enable = true;

        onActivation = {
          cleanup = "zap";
          autoUpdate = true;
        };

        taps = [];

        brews = enabledBrews ++ extraBrews;

        casks = enabledCasks ++ extraCasks;

        masApps = enabledMasApps // extraMasApps;
      };
    }
  );
}
