# Apps Suite - Application management suite
{ lib, pkgs, config, namespace, ... }:

with lib;
with lib.${namespace};

let
  cfg = config.${namespace}.suites.apps;

  # Available application categories
  availableCategories = [
    "development"
    "productivity"
    "media"
    "system"
    "communication"
    "creative"
    "gaming"
  ];
in
{
  # Use the apps suite options helper from lib
  options.${namespace}.suites.apps = mkAppsSuiteOptions "apps" availableCategories // {
    # Additional suite-specific options
    enableHomebrew = mkBoolOpt true "Enable Homebrew for application installation";

    enableMasApps = mkBoolOpt true "Enable Mac App Store applications";

    autoUpdate = mkBoolOpt true "Enable automatic updates for Homebrew";

    cleanup = mkEnumOpt ["none" "uninstall" "zap"] "zap" "Homebrew cleanup strategy";

    # Predefined app profiles for common use cases
    profiles = {
      minimal = mkBoolOpt false "Enable minimal app profile (essential apps only)";
      developer = mkBoolOpt false "Enable developer profile (development-focused apps)";
      creative = mkBoolOpt false "Enable creative profile (design and media apps)";
      business = mkBoolOpt false "Enable business profile (productivity and communication apps)";
    };

    # Global application preferences
    preferences = {
      editor = mkStrOpt "vim" "Default system editor";

      disableAnalytics = mkBoolOpt true "Disable analytics for installed applications";
    };

    # Category-specific overrides
    categoryOverrides = {
      development = {
        excludeApps = mkListOpt types.str [] "Apps to exclude from development category";
        extraCasks = mkListOpt types.str [] "Extra Homebrew casks for development";
        extraBrews = mkListOpt types.str [] "Extra Homebrew brews for development";
        extraMasApps = mkAttrsOpt {} "Extra Mac App Store apps for development";
      };

      productivity = {
        excludeApps = mkListOpt types.str [] "Apps to exclude from productivity category";
        extraCasks = mkListOpt types.str [] "Extra Homebrew casks for productivity";
        extraBrews = mkListOpt types.str [] "Extra Homebrew brews for productivity";
        extraMasApps = mkAttrsOpt {} "Extra Mac App Store apps for productivity";
      };

      media = {
        excludeApps = mkListOpt types.str [] "Apps to exclude from media category";
        extraCasks = mkListOpt types.str [] "Extra Homebrew casks for media";
        extraBrews = mkListOpt types.str [] "Extra Homebrew brews for media";
        extraMasApps = mkAttrsOpt {} "Extra Mac App Store apps for media";
      };

      system = {
        excludeApps = mkListOpt types.str [] "Apps to exclude from system category";
        extraCasks = mkListOpt types.str [] "Extra Homebrew casks for system";
        extraBrews = mkListOpt types.str [] "Extra Homebrew brews for system";
        extraMasApps = mkAttrsOpt {} "Extra Mac App Store apps for system";
      };

      communication = {
        excludeApps = mkListOpt types.str [] "Apps to exclude from communication category";
        extraCasks = mkListOpt types.str [] "Extra Homebrew casks for communication";
        extraBrews = mkListOpt types.str [] "Extra Homebrew brews for communication";
        extraMasApps = mkAttrsOpt {} "Extra Mac App Store apps for communication";
      };

      creative = {
        excludeApps = mkListOpt types.str [] "Apps to exclude from creative category";
        extraCasks = mkListOpt types.str [] "Extra Homebrew casks for creative";
        extraBrews = mkListOpt types.str [] "Extra Homebrew brews for creative";
        extraMasApps = mkAttrsOpt {} "Extra Mac App Store apps for creative";
      };

      gaming = {
        excludeApps = mkListOpt types.str [] "Apps to exclude from gaming category";
        extraCasks = mkListOpt types.str [] "Extra Homebrew casks for gaming";
        extraBrews = mkListOpt types.str [] "Extra Homebrew brews for gaming";
        extraMasApps = mkAttrsOpt {} "Extra Mac App Store apps for gaming";
      };
    };
  };

  config = mkIf cfg.enable (
    let
      # Profile-based category selection
      profileCategories =
        if cfg.profiles.minimal then ["productivity" "system"]
        else if cfg.profiles.developer then ["development" "productivity" "system"]
        else if cfg.profiles.creative then ["creative" "media" "productivity" "system"]
        else if cfg.profiles.business then ["productivity" "communication" "system"]
        else cfg.categories;

      # Final categories after applying profiles and exclusions
      finalCategories = subtractLists cfg.excludeCategories profileCategories;

      # Helper function to check if a category should be enabled
      shouldEnableCategory = category: elem category finalCategories;

    in {
      # Enable Darwin apps module when apps suite is enabled
      ${namespace}.apps = {
        enable = true;

        # Enable categories based on suite configuration and profiles
        development = mkIf (shouldEnableCategory "development") {
          enable = true;
          excludeApps = cfg.categoryOverrides.development.excludeApps;
          extraCasks = cfg.categoryOverrides.development.extraCasks;
          extraBrews = cfg.categoryOverrides.development.extraBrews;
          extraMasApps = cfg.categoryOverrides.development.extraMasApps;
        };

        productivity = mkIf (shouldEnableCategory "productivity") {
          enable = true;
          excludeApps = cfg.categoryOverrides.productivity.excludeApps;
          extraCasks = cfg.categoryOverrides.productivity.extraCasks;
          extraBrews = cfg.categoryOverrides.productivity.extraBrews;
          extraMasApps = cfg.categoryOverrides.productivity.extraMasApps;
        };

        media = mkIf (shouldEnableCategory "media") {
          enable = true;
          excludeApps = cfg.categoryOverrides.media.excludeApps;
          extraCasks = cfg.categoryOverrides.media.extraCasks;
          extraBrews = cfg.categoryOverrides.media.extraBrews;
          extraMasApps = cfg.categoryOverrides.media.extraMasApps;
        };

        system = mkIf (shouldEnableCategory "system") {
          enable = true;
          excludeApps = cfg.categoryOverrides.system.excludeApps;
          extraCasks = cfg.categoryOverrides.system.extraCasks;
          extraBrews = cfg.categoryOverrides.system.extraBrews;
          extraMasApps = cfg.categoryOverrides.system.extraMasApps;
        };

        communication = mkIf (shouldEnableCategory "communication") {
          enable = true;
          excludeApps = cfg.categoryOverrides.communication.excludeApps;
          extraCasks = cfg.categoryOverrides.communication.extraCasks;
          extraBrews = cfg.categoryOverrides.communication.extraBrews;
          extraMasApps = cfg.categoryOverrides.communication.extraMasApps;
        };

        creative = mkIf (shouldEnableCategory "creative") {
          enable = true;
          excludeApps = cfg.categoryOverrides.creative.excludeApps;
          extraCasks = cfg.categoryOverrides.creative.extraCasks;
          extraBrews = cfg.categoryOverrides.creative.extraBrews;
          extraMasApps = cfg.categoryOverrides.creative.extraMasApps;
        };

        gaming = mkIf (shouldEnableCategory "gaming") {
          enable = true;
          excludeApps = cfg.categoryOverrides.gaming.excludeApps;
          extraCasks = cfg.categoryOverrides.gaming.extraCasks;
          extraBrews = cfg.categoryOverrides.gaming.extraBrews;
          extraMasApps = cfg.categoryOverrides.gaming.extraMasApps;
        };
      };
    }
  );
}
