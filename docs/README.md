# 📚 文檔目錄

這個資料夾包含了 Nix 配置系統的完整文檔。

## 📖 主要文檔

### 用戶文檔
- **[架構指南](ARCHITECTURE_GUIDE.md)** - 系統架構設計和核心理念
- **[使用指南](USER_GUIDE.md)** - 完整的配置和使用教學
- **[快速參考](QUICK_REFERENCE.md)** - 常用命令和配置速查

## 📁 子目錄

### 🛠️ [development/](development/)
開發相關文檔：
- **[模組開發指南](development/MODULE_DEVELOPMENT_GUIDE.md)** - 如何開發新模組
- **[故障排除](development/TROUBLESHOOTING.md)** - 常見問題和解決方案

### 📊 [reports/](reports/)
項目開發過程中的報告和記錄：
- 功能實現報告
- 測試結果報告
- 架構變更記錄
- 問題解決報告

## 🎯 文檔使用指南

### 對於新用戶
1. 先閱讀 **[快速參考](QUICK_REFERENCE.md)** 快速上手
2. 詳細了解請參考 **[使用指南](USER_GUIDE.md)**
3. 深入理解可閱讀 **[架構指南](ARCHITECTURE_GUIDE.md)**

### 對於開發者
1. 閱讀 **[架構指南](ARCHITECTURE_GUIDE.md)** 了解系統設計
2. 參考 **[模組開發指南](development/MODULE_DEVELOPMENT_GUIDE.md)** 開發新功能
3. 遇到問題時查看 **[故障排除](development/TROUBLESHOOTING.md)**

### 對於維護者
1. 查看 **[reports/](reports/)** 了解項目歷史
2. 參考各種實現報告了解設計決策
3. 使用測試報告驗證功能完整性

## 📝 文檔維護

### 更新原則
- 代碼變更時同步更新相關文檔
- 新功能必須包含使用說明
- 重要變更需要記錄在報告中

### 文檔結構
- **主要文檔**: 面向用戶的核心文檔
- **開發文檔**: 面向開發者的技術文檔
- **報告文檔**: 項目過程記錄和歷史資料

## 🔗 相關連結

- [主項目 README](../README.md) - 項目概述和快速開始
- [模組目錄](../modules/) - 實際的模組實現
- [配置範例](../homes/) - 用戶配置範例

---

💡 **提示**: 如果您是第一次使用，建議從 [快速參考](QUICK_REFERENCE.md) 開始！
